# Run CI checks on all pushes and pull requests

name: CI

on:
  pull_request: ~
  push:
    branches:
      - main

jobs:
  prettier:
    name: Prettier
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/prepare
      - run: pnpm format-check
  type_check:
    name: Type Check
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/prepare
      - run: pnpm typecheck
  tests:
    name: Tests
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/prepare
      - run: pnpm test
