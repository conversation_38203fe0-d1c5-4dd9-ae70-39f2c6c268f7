{"name": "zod-v3-to-v4", "version": "0.3.0", "description": "Migrate Zod from v3 to v4", "keywords": ["zod", "codemod", "upgrade", "migrate"], "homepage": "https://github.com/nicoespeon/zod-v3-to-v4", "bugs": "https://github.com/nicoespeon/zod-v3-to-v4/issues", "repository": {"type": "git", "url": "git+https://github.com/nicoespeon/zod-v3-to-v4.git"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "type": "module", "main": "dist/src/index.js", "bin": {"zod-v3-to-v4": "dist/src/index.js"}, "files": ["README.md", "dist/src/"], "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "format": "prettier . --write", "format-check": "prettier . --list-different", "playground": "pnpm playground:interactive playground/tsconfig.json", "playground:interactive": "node --experimental-strip-types --disable-warning=ExperimentalWarning src/index.ts", "prepare": "husky", "prepublishOnly": "pnpm typecheck && pnpm test && pnpm build", "test": "vitest run", "test:watch": "vitest watch", "typecheck": "tsc --noEmit"}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "dependencies": {"@clack/prompts": "1.0.0-alpha.1", "ts-morph": "26.0.0"}, "devDependencies": {"@types/node": "20.9.0", "husky": "9.1.7", "lint-staged": "16.0.0", "prettier": "3.5.3", "prettier-plugin-curly": "0.3.1", "prettier-plugin-packagejson": "2.5.10", "prettier-plugin-sh": "0.15.0", "rimraf": "6.0.1", "typescript": "5.8.3", "vitest": "3.1.4", "zod": "3.25.12"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}